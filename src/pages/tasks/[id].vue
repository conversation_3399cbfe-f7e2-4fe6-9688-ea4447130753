<template>
  <v-container class="pa-6" fluid>
    <!-- Loading State -->
    <div v-if="loading" class="d-flex justify-center align-center" style="min-height: 400px;">
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </div>

    <!-- Error State -->
    <v-alert
      v-else-if="error"
      class="mb-4"
      type="error"
      variant="tonal"
    >
      {{ error }}
    </v-alert>

    <!-- Task Not Found -->
    <v-card v-else-if="!task" class="text-center pa-8" elevation="2">
      <v-icon class="mb-4" color="grey-lighten-2" size="64">
        mdi-file-question-outline
      </v-icon>
      <h2 class="text-h5 mb-4">Task Not Found</h2>
      <p class="text-body-1 text-medium-emphasis mb-6">
        The requested task could not be found in the database.
      </p>
      <v-btn color="primary" @click="$router.push('/')">
        <v-icon start>mdi-arrow-left</v-icon>
        Back to Tasks
      </v-btn>
    </v-card>

    <!-- Task Details -->
    <div v-else>
      <!-- Header with Navigation -->
      <v-row class="mb-4">
        <v-col>
          <div class="d-flex align-center justify-space-between">
            <v-btn
              class="text-none"
              prepend-icon="mdi-arrow-left"
              variant="text"
              @click="$router.push('/')"
            >
              Back to Tasks
            </v-btn>

            <div class="d-flex align-center ga-2">
              <v-chip
                :color="getPriorityColor(task.priority)"
                size="small"
                variant="elevated"
              >
                {{ task.priority }} Priority
              </v-chip>
              <v-chip
                :color="getStatusColor(task.status)"
                size="small"
                variant="tonal"
              >
                {{ task.status }}
              </v-chip>

              <!-- Edit Mode Toggle Button -->
              <v-btn
                v-if="!isEditMode"
                color="primary"
                prepend-icon="mdi-pencil"
                size="small"
                variant="outlined"
                @click="enterEditMode"
              >
                Edit
              </v-btn>

              <!-- Save/Cancel Buttons in Edit Mode -->
              <div v-else class="d-flex ga-2">
                <v-btn
                  color="success"
                  :loading="editLoading"
                  prepend-icon="mdi-check"
                  size="small"
                  variant="elevated"
                  @click="saveTask"
                >
                  Save
                </v-btn>
                <v-btn
                  color="grey"
                  :disabled="editLoading"
                  prepend-icon="mdi-close"
                  size="small"
                  variant="outlined"
                  @click="cancelEdit"
                >
                  Cancel
                </v-btn>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Task Header Card -->
      <v-card class="mb-6" elevation="2">
        <v-card-title class="d-flex align-start justify-space-between">
          <div class="flex-1-1">
            <div class="d-flex align-center mb-2">
              <v-avatar class="me-3" :color="getPriorityColor(task.priority)" size="40">
                <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
              </v-avatar>
              <div class="flex-1-1">
                <!-- View Mode -->
                <div v-if="!isEditMode">
                  <div class="text-h5 font-weight-bold">{{ task.summary }}</div>
                  <div class="text-body-2 text-medium-emphasis">{{ task.task_id }}</div>
                </div>

                <!-- Edit Mode -->
                <div v-else>
                  <v-text-field
                    v-model="editForm.summary"
                    class="mb-2"
                    density="compact"
                    hide-details="auto"
                    label="Summary"
                    :rules="rules.summary"
                    variant="outlined"
                  />
                  <div class="text-body-2 text-medium-emphasis">{{ task.task_id }} (Read-only)</div>
                </div>
              </div>
            </div>
          </div>

          <v-menu>
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                icon="mdi-dots-vertical"
                variant="text"
              />
            </template>
            <v-list>
              <v-list-item @click="updateStatus('Backlog')">
                <v-list-item-title>Move to Backlog</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('In Progress')">
                <v-list-item-title>Start Progress</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Done')">
                <v-list-item-title>Mark Done</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Blocked')">
                <v-list-item-title>Mark Blocked</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-card-title>

        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <div class="mb-4">
                <h4 class="text-subtitle-1 font-weight-medium mb-2">Task Details</h4>

                <!-- View Mode -->
                <v-table v-if="!isEditMode" density="compact">
                  <tbody>
                    <tr>
                      <td class="font-weight-medium">Type</td>
                      <td>
                        <v-chip size="small" variant="outlined">
                          {{ task.type }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Priority</td>
                      <td>
                        <v-chip :color="getPriorityColor(task.priority)" size="small">
                          {{ task.priority }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Status</td>
                      <td>
                        <v-chip :color="getStatusColor(task.status)" size="small" variant="tonal">
                          {{ task.status }}
                        </v-chip>
                      </td>
                    </tr>
                    <tr v-if="task.estimated_effort">
                      <td class="font-weight-medium">Estimated Effort</td>
                      <td>{{ task.estimated_effort }}</td>
                    </tr>
                    <tr v-if="task.epic">
                      <td class="font-weight-medium">Epic</td>
                      <td>
                        <v-chip color="primary" size="small" variant="text">
                          {{ task.epic }}
                        </v-chip>
                      </td>
                    </tr>
                  </tbody>
                </v-table>

                <!-- Edit Mode -->
                <div v-else class="d-flex flex-column ga-3">
                  <v-select
                    v-model="editForm.type"
                    density="compact"
                    hide-details
                    :items="typeOptions"
                    label="Type"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.priority"
                    density="compact"
                    hide-details
                    :items="priorityOptions"
                    label="Priority"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.status"
                    density="compact"
                    hide-details
                    :items="statusOptions"
                    label="Status"
                    variant="outlined"
                  />

                  <v-select
                    v-model="editForm.estimated_effort"
                    density="compact"
                    hide-details
                    :items="effortOptions"
                    label="Estimated Effort"
                    variant="outlined"
                  />

                  <v-text-field
                    v-model="editForm.epic"
                    density="compact"
                    hide-details
                    label="Epic"
                    placeholder="Optional epic name"
                    variant="outlined"
                  />
                </div>
              </div>
            </v-col>

            <v-col cols="12" md="6">
              <div class="mb-4">
                <h4 class="text-subtitle-1 font-weight-medium mb-2">Timestamps</h4>
                <v-table density="compact">
                  <tbody>
                    <tr>
                      <td class="font-weight-medium">Created</td>
                      <td>{{ formatDate(task.created_at) }}</td>
                    </tr>
                    <tr>
                      <td class="font-weight-medium">Updated</td>
                      <td>{{ formatDate(task.updated_at) }}</td>
                    </tr>
                  </tbody>
                </v-table>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <!-- Linked Tasks -->
      <v-card v-if="task.linked_tasks && task.linked_tasks.length > 0" class="mb-6" elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-link-variant</v-icon>
          Linked Tasks ({{ task.linked_tasks.length }})
        </v-card-title>
        <v-card-text>
          <div class="d-flex flex-wrap ga-2">
            <v-chip
              v-for="linkedTask in task.linked_tasks"
              :key="linkedTask.task_id"
              class="cursor-pointer"
              variant="outlined"
              @click="navigateToTask(linkedTask.task_id)"
            >
              <v-icon start>mdi-open-in-new</v-icon>
              {{ linkedTask.task_id }} ({{ linkedTask.linkType }})
            </v-chip>
          </div>
        </v-card-text>
      </v-card>

      <!-- Task Description -->
      <v-card elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-text-box-outline</v-icon>
          Description
          <v-spacer />

          <!-- View Mode Toggle (only in view mode) -->
          <v-btn-toggle
            v-if="!isEditMode"
            v-model="viewMode"
            density="compact"
            mandatory
            variant="outlined"
          >
            <v-btn icon="mdi-eye" title="Rendered View" value="rendered" />
            <v-btn icon="mdi-code-tags" title="Markdown Source" value="markdown" />
          </v-btn-toggle>

          <!-- Edit Mode Indicator -->
          <v-chip v-else color="primary" size="small" variant="tonal">
            <v-icon start>mdi-pencil</v-icon>
            Editing
          </v-chip>
        </v-card-title>

        <v-card-text class="task-description">
          <!-- Edit Mode -->
          <div v-if="isEditMode">
            <v-textarea
              v-model="editForm.description"
              auto-grow
              hide-details
              label="Description (Markdown supported)"
              placeholder="Enter task description using Markdown syntax..."
              rows="8"
              variant="outlined"
            />

            <!-- Markdown Help -->
            <v-expansion-panels class="mt-3" variant="accordion">
              <v-expansion-panel title="Markdown Help">
                <v-expansion-panel-text>
                  <div class="text-body-2">
                    <p><strong>Common Markdown Syntax:</strong></p>
                    <ul>
                      <li><code># Header 1</code>, <code>## Header 2</code>, <code>### Header 3</code></li>
                      <li><code>**bold text**</code> or <code>__bold text__</code></li>
                      <li><code>*italic text*</code> or <code>_italic text_</code></li>
                      <li><code>`inline code`</code></li>
                      <li><code>- List item</code> or <code>* List item</code></li>
                      <li><code>1. Numbered list</code></li>
                      <li><code>[Link text](URL)</code></li>
                      <li><code>```code block```</code></li>
                      <li><code>&gt; Blockquote</code></li>
                    </ul>
                  </div>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>

            <!-- Live Preview -->
            <div v-if="editForm.description" class="mt-4">
              <h4 class="text-subtitle-2 mb-2">Preview:</h4>
              <v-card class="markdown-preview pa-4" variant="outlined">
                <Markdown :source="editForm.description" />
              </v-card>
            </div>
          </div>

          <!-- View Mode -->
          <div v-else>
            <!-- No Description State -->
            <div v-if="!task.description" class="text-center py-8">
              <v-icon class="mb-3" color="grey-lighten-2" size="48">
                mdi-text-box-remove-outline
              </v-icon>
              <p class="text-body-1 text-medium-emphasis">
                No description provided for this task.
              </p>
            </div>

            <!-- Rendered Markdown View -->
            <div v-else-if="viewMode === 'rendered'" class="markdown-content">
              <Markdown :source="task.description" />
            </div>

            <!-- Raw Markdown View -->
            <div v-else class="markdown-source">
              <pre class="text-body-2"><code>{{ task.description }}</code></pre>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </div>

    <!-- Status Update Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
    >
      {{ snackbarMessage }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import Markdown from 'vue3-markdown-it'
  import { useRoute, useRouter } from 'vue-router'
  import { useTasksStore } from '@/stores/tasks'

  const route = useRoute()
  const router = useRouter()
  const tasksStore = useTasksStore()

  // Reactive data
  const task = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const viewMode = ref('rendered')
  const showSnackbar = ref(false)
  const snackbarMessage = ref('')
  const snackbarColor = ref('success')

  // Edit mode state
  const isEditMode = ref(false)
  const editLoading = ref(false)
  const editForm = ref({
    summary: '',
    description: '',
    type: '',
    priority: '',
    status: '',
    estimated_effort: '',
    epic: '',
    linked_tasks: [],
  })
  const originalTask = ref(null)

  // Form validation rules
  const rules = {
    summary: [
      v => !!v || 'Summary is required',
      v => (v && v.length >= 3) || 'Summary must be at least 3 characters',
    ],
  }

  // Options for select fields
  const typeOptions = ['Story', 'Task', 'Epic', 'Bug']
  const priorityOptions = ['High', 'Medium', 'Low']
  const statusOptions = ['Backlog', 'In Progress', 'Done', 'Blocked']
  const effortOptions = ['Large', 'Medium', 'Small']

  // Methods
  const fetchTask = async taskId => {
    loading.value = true
    error.value = null

    try {
      const foundTask = await tasksStore.getTaskById(taskId)

      if (foundTask) {
        task.value = foundTask
      } else {
        error.value = `Task with ID "${taskId}" not found`
      }
    } catch (error_) {
      error.value = `Failed to load task: ${error_.message}`
      console.error('Error fetching task:', error_)
    } finally {
      loading.value = false
    }
  }

  const updateStatus = async newStatus => {
    if (!task.value) return

    try {
      await tasksStore.updateTaskStatus(task.value.task_id, newStatus)

      // Update local task object
      task.value.status = newStatus
      task.value.updated_at = new Date().toISOString()

      // Show success message
      snackbarMessage.value = `Task status updated to "${newStatus}"`
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (error) {
      snackbarMessage.value = `Failed to update task status: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to update task status:', error)
    }
  }

  const navigateToTask = async taskId => {
    if (taskId === task.value?.task_id) return // Don't navigate to self

    await router.push(`/tasks/${taskId}`)
  }

  const getPriorityColor = priority => {
    switch (priority) {
      case 'High': { return 'error'
      }
      case 'Medium': { return 'warning'
      }
      case 'Low': { return 'success'
      }
      default: { return 'grey'
      }
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'Done': { return 'success'
      }
      case 'In Progress': { return 'info'
      }
      case 'Blocked': { return 'error'
      }
      case 'Backlog': { return 'grey'
      }
      default: { return 'grey'
      }
    }
  }

  const getTypeIcon = type => {
    switch (type) {
      case 'Story': { return 'mdi-book-open-page-variant'
      }
      case 'Task': { return 'mdi-checkbox-marked-circle'
      }
      case 'Epic': { return 'mdi-flag'
      }
      case 'Bug': { return 'mdi-bug'
      }
      default: { return 'mdi-file-document'
      }
    }
  }

  const formatDate = dateString => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  // Edit mode methods
  const enterEditMode = () => {
    if (!task.value) return

    // Store original task for cancellation
    originalTask.value = { ...task.value }

    // Populate edit form with current task data
    editForm.value = {
      summary: task.value.summary || '',
      description: task.value.description || '',
      type: task.value.type || 'Task',
      priority: task.value.priority || 'Medium',
      status: task.value.status || 'Backlog',
      estimated_effort: task.value.estimated_effort || 'Medium',
      epic: task.value.epic || '',
      linked_tasks: task.value.linked_tasks || [],
    }

    isEditMode.value = true
  }

  const cancelEdit = () => {
    isEditMode.value = false
    editForm.value = {
      summary: '',
      description: '',
      type: '',
      priority: '',
      status: '',
      estimated_effort: '',
      epic: '',
      linked_tasks: [],
    }
    originalTask.value = null
  }

  const saveTask = async () => {
    // Validate required fields
    if (!editForm.value.summary || editForm.value.summary.length < 3) {
      snackbarMessage.value = 'Summary is required and must be at least 3 characters'
      snackbarColor.value = 'error'
      showSnackbar.value = true
      return
    }

    editLoading.value = true

    try {
      // Prepare update data (exclude task_id as it's read-only)
      const updateData = {
        summary: editForm.value.summary,
        description: editForm.value.description || null,
        type: editForm.value.type,
        priority: editForm.value.priority,
        status: editForm.value.status,
        estimated_effort: editForm.value.estimated_effort,
        epic: editForm.value.epic || null,
        linked_tasks: editForm.value.linked_tasks,
      }

      // Update task via store
      const updatedTask = await tasksStore.updateTask(task.value.task_id, updateData)

      if (updatedTask) {
        // Update local task object
        task.value = { ...task.value, ...updatedTask }

        // Exit edit mode
        isEditMode.value = false
        originalTask.value = null

        // Show success message
        snackbarMessage.value = 'Task updated successfully'
        snackbarColor.value = 'success'
        showSnackbar.value = true
      } else {
        throw new Error('Failed to update task')
      }
    } catch (error) {
      snackbarMessage.value = `Failed to update task: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to update task:', error)
    } finally {
      editLoading.value = false
    }
  }

  // Lifecycle
  onMounted(() => {
    const taskId = route.params.id
    if (taskId) {
      fetchTask(taskId)
    }
  })

  // Watch for route changes
  watch(() => route.params.id, newId => {
    if (newId) {
      fetchTask(newId)
    }
  })
</script>

<style scoped>
.task-description {
  max-width: none;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.markdown-content :deep(h1) {
  font-size: 2rem;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.3rem;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.25rem;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  font-style: italic;
}

.markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.markdown-content :deep(a) {
  color: #2196f3;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-source {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 1rem;
}

.markdown-source pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.markdown-source code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.cursor-pointer {
  cursor: pointer;
}

.markdown-preview {
  max-height: 300px;
  overflow-y: auto;
}

.markdown-preview .markdown-content {
  line-height: 1.6;
}

.markdown-preview .markdown-content :deep(h1),
.markdown-preview .markdown-content :deep(h2),
.markdown-preview .markdown-content :deep(h3),
.markdown-preview .markdown-content :deep(h4),
.markdown-preview .markdown-content :deep(h5),
.markdown-preview .markdown-content :deep(h6) {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.markdown-preview .markdown-content :deep(p) {
  margin-bottom: 0.5rem;
}

.markdown-preview .markdown-content :deep(ul),
.markdown-preview .markdown-content :deep(ol) {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.markdown-preview .markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-preview .markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.markdown-preview .markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 0.5rem 0;
  padding: 0.25rem 0.5rem;
  background-color: #f5f5f5;
  font-style: italic;
}
</style>
