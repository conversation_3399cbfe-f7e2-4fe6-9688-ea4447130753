<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">My Tasks</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Manage and track your project tasks
            </p>
          </div>
          <div class="d-flex align-center ga-2">
            <v-btn
              color="primary"
              prepend-icon="mdi-file-upload"
              variant="elevated"
              @click="$router.push('/upload')"
            >
              Import Tasks
            </v-btn>
            <v-btn
              :color="isHierarchicalView ? 'secondary' : 'primary'"
              :prepend-icon="isHierarchicalView ? 'mdi-format-list-bulleted' : 'mdi-sitemap'"
              variant="elevated"
              @click="toggleHierarchicalView"
            >
              {{ isHierarchicalView ? 'Flat View' : 'Hierarchical View' }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row v-if="tasksStore.tasks.length > 0" class="mb-6">
      <v-col cols="12" md="3">
        <v-card color="primary" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-format-list-checks</v-icon>
            <div class="text-h4 font-weight-bold">{{ tasksStore.statistics.total }}</div>
            <div class="text-subtitle-1">Total Tasks</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="success" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-check-circle</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Done?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="warning" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-clock-outline</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus['In Progress']?.length || 0 }}
            </div>
            <div class="text-subtitle-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card color="info" variant="elevated">
          <v-card-text class="text-center">
            <v-icon class="mb-2" size="48">mdi-playlist-plus</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Backlog?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Backlog</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Filters and Search -->
    <TaskFilters
      v-if="tasksStore.tasks.length > 0"
      v-model="filters"
      v-model:view-mode="viewMode"
      :tasks="tasksStore.tasks"
      :total-tasks-count="tasksStore.tasks.length"
    />

    <!-- Tasks List -->
    <v-row v-if="filteredTasks.length > 0">
      <v-col>
        <v-card elevation="2">
          <v-card-title>
            Tasks ({{ filteredTasks.length }})
          </v-card-title>

          <!-- List View -->
          <div v-if="viewMode === 'list'">
            <v-list>
              <template v-for="(task, index) in filteredTasks" :key="task.task_id">
                <v-list-item
                  class="py-3 cursor-pointer"
                  :style="{ paddingLeft: `${6 + (task.level || 0) * 2}px` }"
                  @click="viewTaskDetails(task.task_id)"
                >
                  <template #prepend>
                    <v-avatar :color="getPriorityColor(task.priority)" size="40">
                      <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
                    </v-avatar>
                  </template>

                  <v-list-item-title class="font-weight-medium">
                    {{ task.summary }}
                  </v-list-item-title>

                  <v-list-item-subtitle class="mt-1">
                    <div class="text-body-2 mb-1">{{ task.task_id }}</div>
                    <div class="d-flex align-center flex-wrap ga-2">
                      <v-chip :color="getPriorityColor(task.priority)" size="small">
                        {{ task.priority }}
                      </v-chip>
                      <v-chip size="small" variant="outlined">
                        {{ task.type }}
                      </v-chip>
                      <v-chip
                        :color="getStatusColor(task.status)"
                        size="small"
                        variant="tonal"
                      >
                        {{ task.status }}
                      </v-chip>
                      <span v-if="task.epic" class="text-caption">
                        Epic: {{ task.epic }}
                      </span>
                    </div>
                  </v-list-item-subtitle>

                  <template #append>
                    <div class="d-flex align-center ga-2">
                      <v-tooltip text="View Details">
                        <template #activator="{ props }">
                          <v-btn
                            v-bind="props"
                            icon="mdi-eye"
                            size="small"
                            variant="text"
                            @click.stop="viewTaskDetails(task.task_id)"
                          />
                        </template>
                      </v-tooltip>
                      <v-menu>
                        <template #activator="{ props }">
                          <v-btn
                            v-bind="props"
                            icon="mdi-dots-vertical"
                            size="small"
                            variant="text"
                            @click.stop
                          />
                        </template>
                        <v-list>
                          <v-list-item @click="updateStatus(task, 'Backlog')">
                            <v-list-item-title>Move to Backlog</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'In Progress')">
                            <v-list-item-title>Start Progress</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'Done')">
                            <v-list-item-title>Mark Done</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'Blocked')">
                            <v-list-item-title>Mark Blocked</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </div>
                  </template>
                </v-list-item>
                <v-divider v-if="index < filteredTasks.length - 1" />
              </template>
            </v-list>
          </div>

          <!-- Grid View -->
          <div v-else class="pa-4">
            <v-row>
              <v-col
                v-for="task in filteredTasks"
                :key="task.task_id"
                cols="12"
                lg="4"
                md="6"
              >
                <v-card
                  :border="getPriorityColor(task.priority)"
                  class="h-100 cursor-pointer"
                  elevation="1"
                  hover
                  @click="viewTaskDetails(task.task_id)"
                >
                  <v-card-title class="text-wrap">
                    <div class="d-flex align-start justify-space-between">
                      <span class="flex-1-1">{{ task.summary }}</span>
                      <v-chip
                        class="ml-2"
                        :color="getPriorityColor(task.priority)"
                        size="small"
                      >
                        {{ task.priority }}
                      </v-chip>
                    </div>
                  </v-card-title>

                  <v-card-text>
                    <div class="mb-3">
                      <div class="text-body-2 text-medium-emphasis mb-2">{{ task.task_id }}</div>
                      <p v-if="task.description" class="text-body-2 text-medium-emphasis">
                        {{ truncateText(task.description, 100) }}
                      </p>
                    </div>

                    <div class="d-flex flex-wrap ga-1 mb-3">
                      <v-chip size="x-small" variant="outlined">
                        {{ task.type }}
                      </v-chip>
                      <v-chip
                        :color="getStatusColor(task.status)"
                        size="x-small"
                        variant="tonal"
                      >
                        {{ task.status }}
                      </v-chip>
                      <v-chip v-if="task.epic" color="primary" size="x-small" variant="text">
                        {{ task.epic }}
                      </v-chip>
                    </div>
                  </v-card-text>

                  <v-card-actions>
                    <v-btn
                      color="primary"
                      variant="text"
                      @click.stop="viewTaskDetails(task.task_id)"
                    >
                      <v-icon start>mdi-eye</v-icon>
                      View Details
                    </v-btn>
                    <v-spacer />
                    <v-menu>
                      <template #activator="{ props }">
                        <v-btn
                          v-bind="props"
                          size="small"
                          variant="outlined"
                          @click.stop
                        >
                          Change Status
                          <v-icon end>mdi-chevron-down</v-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item @click="updateStatus(task, 'Backlog')">
                          <v-list-item-title>Backlog</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'In Progress')">
                          <v-list-item-title>In Progress</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'Done')">
                          <v-list-item-title>Done</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'Blocked')">
                          <v-list-item-title>Blocked</v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else-if="tasksStore.tasks.length === 0 && !tasksStore.loading">
      <v-col>
        <v-card class="text-center pa-12" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="120">
            mdi-format-list-checks-outline
          </v-icon>
          <h2 class="text-h5 mb-4">No Tasks Yet</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Import your first JSON file to start managing tasks.
          </p>
          <v-btn
            color="primary"
            size="large"
            variant="elevated"
            @click="$router.push('/upload')"
          >
            <v-icon start>mdi-file-upload</v-icon>
            Import Tasks
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- No Results Found -->
    <v-row v-else-if="filteredTasks.length === 0 && tasksStore.tasks.length > 0">
      <v-col>
        <v-card class="text-center pa-8" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="64">
            mdi-filter-remove-outline
          </v-icon>
          <h2 class="text-h6 mb-4">No Tasks Match Your Filters</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Try adjusting your search terms or filters to find tasks.
          </p>
          <v-btn
            variant="outlined"
            @click="clearFilters"
          >
            Clear Filters
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="tasksStore.loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup>
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import TaskFilters from '@/components/TaskFilters.vue'
  import { useTasksStore } from '@/stores/tasks'

  // Router
  const router = useRouter()

  // Store
  const tasksStore = useTasksStore()

  // Reactive data
  const viewMode = ref('list')
  const isHierarchicalView = ref(false)

  const filters = ref({
    search: '',
    type: 'All',
    priority: 'All',
    status: 'All',
    epic: 'All',
  })

  // Computed properties
  const filteredTasks = computed(() => {
    let tasks = tasksStore.tasks

    // Apply search
    if (filters.value.search) {
      tasks = tasksStore.searchTasks(tasks, filters.value.search)
    }

    // Apply filters
    tasks = tasksStore.filterTasks(tasks, filters.value)

    // Apply hierarchical sorting if enabled
    if (isHierarchicalView.value) {
      tasks = tasksStore.getHierarchicalTasks(tasks)
    }

    return tasks
  })

  // Methods
  const toggleHierarchicalView = () => {
    isHierarchicalView.value = !isHierarchicalView.value
  }
  const viewTaskDetails = taskId => {
    router.push(`/tasks/${taskId}`)
  }

  const updateStatus = async (task, newStatus) => {
    try {
      await tasksStore.updateTaskStatus(task.task_id, newStatus)
    } catch (error) {
      console.error('Failed to update task status:', error)
    }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      type: 'All',
      priority: 'All',
      status: 'All',
      epic: 'All',
    }
  }

  const getPriorityColor = priority => {
    switch (priority) {
      case 'High': { return 'error'
      }
      case 'Medium': { return 'warning'
      }
      case 'Low': { return 'success'
      }
      default: { return 'grey'
      }
    }
  }

  const getStatusColor = status => {
    switch (status) {
      case 'Done': { return 'success'
      }
      case 'In Progress': { return 'info'
      }
      case 'Blocked': { return 'error'
      }
      case 'Backlog': { return 'grey'
      }
      default: { return 'grey'
      }
    }
  }

  const getTypeIcon = type => {
    switch (type) {
      case 'Story': { return 'mdi-book-open-page-variant'
      }
      case 'Task': { return 'mdi-checkbox-marked-circle'
      }
      case 'Epic': { return 'mdi-flag'
      }
      case 'Bug': { return 'mdi-bug'
      }
      default: { return 'mdi-file-document'
      }
    }
  }

  const truncateText = (text, maxLength) => {
    if (!text) return ''
    return text.length > maxLength ? text.slice(0, Math.max(0, maxLength)) + '...' : text
  }

  // Lifecycle
  onMounted(() => {
    tasksStore.fetchTasks()
    // Load filters from localStorage
    const savedFilters = localStorage.getItem('taskFilters')
    if (savedFilters) {
      filters.value = JSON.parse(savedFilters)
    }
  })

  onBeforeUnmount(() => {
    // Save filters to localStorage before component is unmounted
    localStorage.setItem('taskFilters', JSON.stringify(filters.value))
  })
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}

.v-chip {
  font-weight: 500;
}

.text-wrap {
  word-break: break-word;
  white-space: normal;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
