<template>
  <v-container fluid class="pa-6">
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">Task Manager</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Upload and manage your project tasks from JSON files
            </p>
          </div>
          <v-chip
            v-if="tasksStore.tasks.length > 0"
            color="primary"
            variant="elevated"
            size="large"
          >
            {{ tasksStore.tasks.length }} Tasks
          </v-chip>
        </div>
      </v-col>
    </v-row>

    <!-- File Upload Section -->
    <v-row class="mb-6">
      <v-col>
        <!-- Info Card -->
        <TaskUploadInfo />
        
        <v-card elevation="2" class="mb-4">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="primary">mdi-upload</v-icon>
            Upload Tasks JSON File
          </v-card-title>
          
          <v-card-text>
            <div class="text-center pa-6">
              <v-file-input
                v-model="selectedFile"
                accept=".json"
                label="Select JSON file"
                prepend-icon="mdi-file-document"
                variant="outlined"
                :loading="tasksStore.loading"
                :disabled="tasksStore.uploadStatus === 'processing'"
                @change="handleFileSelect"
                class="mb-4"
              />
              
              <!-- Upload Progress -->
              <div v-if="tasksStore.uploadStatus === 'processing'" class="mb-4">
                <v-progress-linear
                  :model-value="tasksStore.uploadProgress"
                  color="primary"
                  height="8"
                  rounded
                />
                <p class="text-caption mt-2">Processing tasks...</p>
              </div>

              <!-- Upload Button -->
              <v-btn
                :disabled="!selectedFile || tasksStore.uploadStatus === 'processing'"
                :loading="tasksStore.uploadStatus === 'processing'"
                color="primary"
                size="large"
                variant="elevated"
                @click="processUpload"
              >
                <v-icon start>mdi-database-import</v-icon>
                Process Upload
              </v-btn>

              <!-- Clear Database Button -->
              <v-btn
                v-if="tasksStore.tasks.length > 0"
                variant="outlined"
                color="error"
                class="ml-4"
                @click="showClearDialog = true"
              >
                <v-icon start>mdi-delete</v-icon>
                Clear Database
              </v-btn>
            </div>

            <!-- Upload Result -->
            <v-alert
              v-if="tasksStore.uploadStatus === 'success' && tasksStore.lastUploadResult"
              type="success"
              variant="tonal"
              class="mt-4"
            >
              <v-alert-title>Upload Successful!</v-alert-title>
              Successfully imported {{ tasksStore.lastUploadResult.successful }} tasks.
              <div v-if="tasksStore.lastUploadResult.failed > 0" class="mt-2">
                <strong>{{ tasksStore.lastUploadResult.failed }} tasks failed:</strong>
                <ul class="mt-1">
                  <li v-for="error in tasksStore.lastUploadResult.errors" :key="error">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </v-alert>

            <!-- Error Display -->
            <v-alert
              v-if="tasksStore.error"
              type="error"
              variant="tonal"
              class="mt-4"
              closable
              @click:close="tasksStore.error = null"
            >
              {{ tasksStore.error }}
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row v-if="tasksStore.tasks.length > 0" class="mb-6">
      <v-col cols="12" md="3">
        <v-card color="primary" variant="elevated">
          <v-card-text class="text-center">
            <v-icon size="48" class="mb-2">mdi-format-list-checks</v-icon>
            <div class="text-h4 font-weight-bold">{{ tasksStore.statistics.total }}</div>
            <div class="text-subtitle-1">Total Tasks</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card color="success" variant="elevated">
          <v-card-text class="text-center">
            <v-icon size="48" class="mb-2">mdi-check-circle</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Done?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card color="warning" variant="elevated">
          <v-card-text class="text-center">
            <v-icon size="48" class="mb-2">mdi-clock-outline</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus['In Progress']?.length || 0 }}
            </div>
            <div class="text-subtitle-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card color="info" variant="elevated">
          <v-card-text class="text-center">
            <v-icon size="48" class="mb-2">mdi-playlist-plus</v-icon>
            <div class="text-h4 font-weight-bold">
              {{ tasksStore.tasksByStatus.Backlog?.length || 0 }}
            </div>
            <div class="text-subtitle-1">Backlog</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Filters and Search -->
    <v-row v-if="tasksStore.tasks.length > 0" class="mb-4">
      <v-col cols="12" md="4">
        <v-text-field
          v-model="searchQuery"
          label="Search tasks"
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          clearable
        />
      </v-col>
      
      <v-col cols="12" md="2">
        <v-select
          v-model="filters.type"
          :items="typeOptions"
          label="Type"
          variant="outlined"
        />
      </v-col>
      
      <v-col cols="12" md="2">
        <v-select
          v-model="filters.priority"
          :items="priorityOptions"
          label="Priority"
          variant="outlined"
        />
      </v-col>
      
      <v-col cols="12" md="2">
        <v-select
          v-model="filters.status"
          :items="statusOptions"
          label="Status"
          variant="outlined"
        />
      </v-col>
      
      <v-col cols="12" md="2">
        <v-select
          v-model="filters.epic"
          :items="epicOptions"
          label="Epic"
          variant="outlined"
        />
      </v-col>
    </v-row>

    <!-- Tasks List -->
    <v-row v-if="filteredTasks.length > 0">
      <v-col>
        <v-card elevation="2">
          <v-card-title class="d-flex align-center justify-space-between">
            <span>Tasks ({{ filteredTasks.length }})</span>
            <v-btn-toggle v-model="viewMode" mandatory variant="outlined">
              <v-btn value="list" icon="mdi-view-list" />
              <v-btn value="grid" icon="mdi-view-grid" />
            </v-btn-toggle>
          </v-card-title>
          
          <!-- List View -->
          <div v-if="viewMode === 'list'">
            <v-list>
              <template v-for="(task, index) in filteredTasks" :key="task.id">
                <v-list-item 
                  class="py-3 cursor-pointer"
                  @click="viewTaskDetails(task.id)"
                >
                  <template #prepend>
                    <v-avatar :color="getPriorityColor(task.priority)" size="40">
                      <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
                    </v-avatar>
                  </template>
                  
                  <v-list-item-title class="font-weight-medium">
                    {{ task.summary }}
                  </v-list-item-title>
                  
                  <v-list-item-subtitle class="mt-1">
                    <div class="d-flex align-center flex-wrap ga-2">
                      <v-chip size="small" :color="getPriorityColor(task.priority)">
                        {{ task.priority }}
                      </v-chip>
                      <v-chip size="small" variant="outlined">
                        {{ task.type }}
                      </v-chip>
                      <v-chip 
                        size="small" 
                        :color="getStatusColor(task.status)"
                        variant="tonal"
                      >
                        {{ task.status }}
                      </v-chip>
                      <span v-if="task.epic" class="text-caption">
                        Epic: {{ task.epic }}
                      </span>
                    </div>
                  </v-list-item-subtitle>

                  <template #append>
                    <div class="d-flex align-center ga-2">
                      <v-tooltip text="View Details">
                        <template #activator="{ props }">
                          <v-btn
                            v-bind="props"
                            icon="mdi-eye"
                            variant="text"
                            size="small"
                            @click.stop="viewTaskDetails(task.id)"
                          />
                        </template>
                      </v-tooltip>
                      <v-menu>
                        <template #activator="{ props }">
                          <v-btn
                            v-bind="props"
                            icon="mdi-dots-vertical"
                            variant="text"
                            size="small"
                            @click.stop
                          />
                        </template>
                        <v-list>
                          <v-list-item @click="updateStatus(task, 'Backlog')">
                            <v-list-item-title>Move to Backlog</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'In Progress')">
                            <v-list-item-title>Start Progress</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'Done')">
                            <v-list-item-title>Mark Done</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="updateStatus(task, 'Blocked')">
                            <v-list-item-title>Mark Blocked</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </div>
                  </template>
                </v-list-item>
                
                <v-divider v-if="index < filteredTasks.length - 1" />
              </template>
            </v-list>
          </div>

          <!-- Grid View -->
          <div v-else class="pa-4">
            <v-row>
              <v-col
                v-for="task in filteredTasks"
                :key="task.id"
                cols="12"
                md="6"
                lg="4"
              >
                <v-card 
                  elevation="1" 
                  hover
                  :border="getPriorityColor(task.priority)"
                  class="h-100 cursor-pointer"
                  @click="viewTaskDetails(task.id)"
                >
                  <v-card-title class="text-wrap">
                    <div class="d-flex align-start justify-space-between">
                      <span class="flex-1-1">{{ task.summary }}</span>
                      <v-chip
                        size="small"
                        :color="getPriorityColor(task.priority)"
                        class="ml-2"
                      >
                        {{ task.priority }}
                      </v-chip>
                    </div>
                  </v-card-title>
                  
                  <v-card-text>
                    <div class="mb-3">
                      <p v-if="task.description" class="text-body-2 text-medium-emphasis">
                        {{ truncateText(task.description, 100) }}
                      </p>
                    </div>
                    
                    <div class="d-flex flex-wrap ga-1 mb-3">
                      <v-chip size="x-small" variant="outlined">
                        {{ task.type }}
                      </v-chip>
                      <v-chip 
                        size="x-small" 
                        :color="getStatusColor(task.status)"
                        variant="tonal"
                      >
                        {{ task.status }}
                      </v-chip>
                      <v-chip v-if="task.epic" size="x-small" color="primary" variant="text">
                        {{ task.epic }}
                      </v-chip>
                    </div>
                  </v-card-text>
                  
                  <v-card-actions>
                    <v-btn 
                      variant="text" 
                      color="primary"
                      @click.stop="viewTaskDetails(task.id)"
                    >
                      <v-icon start>mdi-eye</v-icon>
                      View Details
                    </v-btn>
                    <v-spacer />
                    <v-menu>
                      <template #activator="{ props }">
                        <v-btn
                          v-bind="props"
                          variant="outlined"
                          size="small"
                          @click.stop
                        >
                          Change Status
                          <v-icon end>mdi-chevron-down</v-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item @click="updateStatus(task, 'Backlog')">
                          <v-list-item-title>Backlog</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'In Progress')">
                          <v-list-item-title>In Progress</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'Done')">
                          <v-list-item-title>Done</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="updateStatus(task, 'Blocked')">
                          <v-list-item-title>Blocked</v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else-if="tasksStore.tasks.length === 0 && !tasksStore.loading">
      <v-col>
        <v-card elevation="1" class="text-center pa-12">
          <v-icon size="120" color="grey-lighten-2" class="mb-4">
            mdi-file-upload-outline
          </v-icon>
          <h2 class="text-h5 mb-4">No Tasks Found</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Upload a JSON file containing your tasks to get started.
          </p>
        </v-card>
      </v-col>
    </v-row>

    <!-- Clear Database Confirmation Dialog -->
    <v-dialog v-model="showClearDialog" max-width="500">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon color="error" class="me-3">mdi-alert</v-icon>
          Clear Database
        </v-card-title>
        
        <v-card-text>
          Are you sure you want to clear all tasks from the database? This action cannot be undone.
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showClearDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="clearDatabase">Clear All</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="tasksStore.loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'
import TaskUploadInfo from '@/components/TaskUploadInfo.vue'

// Router
const router = useRouter()

// Store
const tasksStore = useTasksStore()

// Reactive data
const selectedFile = ref(null)
const searchQuery = ref('')
const viewMode = ref('list')
const showClearDialog = ref(false)

const filters = ref({
  type: 'All',
  priority: 'All', 
  status: 'All',
  epic: 'All'
})

// Computed properties
const typeOptions = computed(() => {
  const types = ['All', ...new Set(tasksStore.tasks.map(task => task.type))]
  return types.sort()
})

const priorityOptions = computed(() => {
  const priorities = ['All', ...new Set(tasksStore.tasks.map(task => task.priority))]
  return priorities.sort()
})

const statusOptions = computed(() => {
  const statuses = ['All', ...new Set(tasksStore.tasks.map(task => task.status))]
  return statuses.sort()
})

const epicOptions = computed(() => {
  const epics = ['All', ...tasksStore.epics]
  return epics
})

const filteredTasks = computed(() => {
  let tasks = tasksStore.tasks

  // Apply search
  if (searchQuery.value) {
    tasks = tasksStore.searchTasks(searchQuery.value)
  }

  // Apply filters
  tasks = tasksStore.filterTasks(filters.value)

  return tasks
})

// Methods
const handleFileSelect = () => {
  tasksStore.resetUploadState()
}

const processUpload = async () => {
  if (!selectedFile.value) return

  try {
    const fileContent = await readFileAsText(selectedFile.value)
    const jsonData = JSON.parse(fileContent)
    
    await tasksStore.uploadTasksFromJson(jsonData)
    
    // Clear file input
    selectedFile.value = null
  } catch (error) {
    if (error instanceof SyntaxError) {
      tasksStore.error = 'Invalid JSON file. Please check the file format.'
    } else {
      tasksStore.error = error.message
    }
    tasksStore.uploadStatus = 'error'
  }
}

const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsText(file)
  })
}

const updateStatus = async (task, newStatus) => {
  try {
    await tasksStore.updateTaskStatus(task.id, newStatus)
  } catch (error) {
    console.error('Failed to update task status:', error)
  }
}

const viewTaskDetails = (taskId) => {
  router.push(`/tasks/${taskId}`)
}

const clearDatabase = async () => {
  try {
    await tasksStore.clearAllTasks()
    showClearDialog.value = false
  } catch (error) {
    console.error('Failed to clear database:', error)
  }
}

const getPriorityColor = (priority) => {
  switch (priority) {
    case 'High': return 'error'
    case 'Medium': return 'warning'
    case 'Low': return 'success'
    default: return 'grey'
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'Done': return 'success'
    case 'In Progress': return 'info'
    case 'Blocked': return 'error'
    case 'Backlog': return 'grey'
    default: return 'grey'
  }
}

const getTypeIcon = (type) => {
  switch (type) {
    case 'Story': return 'mdi-book-open-page-variant'
    case 'Task': return 'mdi-checkbox-marked-circle'
    case 'Epic': return 'mdi-flag'
    case 'Bug': return 'mdi-bug'
    default: return 'mdi-file-document'
  }
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// Lifecycle
onMounted(() => {
  tasksStore.fetchTasks()
})

// Watch for filter changes to reset upload state
watch(filters, () => {
  tasksStore.resetUploadState()
}, { deep: true })
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}

.v-chip {
  font-weight: 500;
}

.text-wrap {
  word-break: break-word;
  white-space: normal;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
