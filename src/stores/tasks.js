/**
 * Tasks Store
 * Manages task state and database operations using Pinia
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import databaseService from '@/services/databaseService'

export const useTasksStore = defineStore('tasks', () => {
  // State
  const tasks = ref([])
  const loading = ref(false)
  const error = ref(null)
  const uploadProgress = ref(0)
  const uploadStatus = ref('idle') // 'idle', 'uploading', 'processing', 'success', 'error'
  const lastUploadResult = ref(null)

  // Computed properties
  const tasksByType = computed(() => {
    const types = {}
    for (const task of tasks.value) {
      if (!types[task.type]) {
        types[task.type] = []
      }
      types[task.type].push(task)
    }
    return types
  })

  const tasksByPriority = computed(() => {
    const priorities = {}
    for (const task of tasks.value) {
      if (!priorities[task.priority]) {
        priorities[task.priority] = []
      }
      priorities[task.priority].push(task)
    }
    return priorities
  })

  const tasksByStatus = computed(() => {
    const statuses = {}
    for (const task of tasks.value) {
      if (!statuses[task.status]) {
        statuses[task.status] = []
      }
      statuses[task.status].push(task)
    }
    return statuses
  })

  const epics = computed(() => {
    const uniqueEpics = new Set()
    for (const task of tasks.value) {
      if (task.epic) {
        uniqueEpics.add(task.epic)
      }
    }
    return Array.from(uniqueEpics).sort()
  })

  const statistics = computed(() => {
    return {
      total: tasks.value.length,
      byType: Object.keys(tasksByType.value).map(type => ({
        type,
        count: tasksByType.value[type].length,
      })),
      byPriority: Object.keys(tasksByPriority.value).map(priority => ({
        priority,
        count: tasksByPriority.value[priority].length,
      })),
      byStatus: Object.keys(tasksByStatus.value).map(status => ({
        status,
        count: tasksByStatus.value[status].length,
      })),
    }
  })

  // Actions
  const fetchTasks = async (filters = {}) => {
    loading.value = true
    error.value = null

    try {
      const fetchedTasks = await databaseService.getAllTasks(filters)
      tasks.value = fetchedTasks
    } catch (error_) {
      error.value = `Failed to fetch tasks: ${error_.message}`
      console.error('Error fetching tasks:', error_)
    } finally {
      loading.value = false
    }
  }

  const uploadTasksFromJson = async jsonData => {
    uploadStatus.value = 'processing'
    uploadProgress.value = 0
    error.value = null
    lastUploadResult.value = null

    try {
      // Validate JSON structure
      if (!Array.isArray(jsonData)) {
        throw new TypeError('JSON data must be an array of tasks')
      }

      if (jsonData.length === 0) {
        throw new Error('No tasks found in the uploaded file')
      }

      // Simulate progress for user feedback
      uploadProgress.value = 25

      // Insert tasks into database
      const result = await databaseService.insertTasksFromJson(jsonData)

      uploadProgress.value = 75

      // Refresh tasks from database
      await fetchTasks()

      uploadProgress.value = 100
      uploadStatus.value = 'success'
      lastUploadResult.value = result

      return result
    } catch (error_) {
      error.value = `Failed to upload tasks: ${error_.message}`
      uploadStatus.value = 'error'
      console.error('Error uploading tasks:', error_)
      throw error_
    }
  }

  const logTaskStatusDistribution = () => {
    const statusCounts = {}
    for (const task of tasks.value) {
      statusCounts[task.status] = (statusCounts[task.status] || 0) + 1
    }
    console.log('Current task status distribution:', statusCounts)
  }

  const updateTaskStatus = async (originalTaskId, newStatus) => {
    try {
      // Find the task in local state to get its PocketBase 'id'
      const taskToUpdate = tasks.value.find(task => task.task_id === originalTaskId)

      if (!taskToUpdate) {
        console.error(`Task with original ID ${originalTaskId} not found.`)
        return false
      }

      console.log(`Updating task ${originalTaskId} from "${taskToUpdate.status}" to "${newStatus}"`)

      // Use the PocketBase 'id' for the database service call
      const success = await databaseService.updateTaskStatus(taskToUpdate.id, newStatus)

      if (success) {
        // Update local state using the originalTaskId
        const taskIndex = tasks.value.findIndex(task => task.task_id === originalTaskId)
        if (taskIndex !== -1) {
          // Create a new object to ensure reactivity
          const updatedTask = { ...tasks.value[taskIndex], status: newStatus }
          // Replace the task in the array to trigger reactivity
          tasks.value.splice(taskIndex, 1, updatedTask)
        }

        // Log status distribution after update
        logTaskStatusDistribution()
      }

      return success
    } catch (error_) {
      error.value = `Failed to update task status: ${error_.message}`
      console.error('Error updating task status:', error_)
      throw error_
    }
  }

  const updateTask = async (taskId, taskData) => {
    try {
      // Find the task in local state to get its PocketBase 'id'
      const taskToUpdate = tasks.value.find(task => task.task_id === taskId || task.id === taskId)

      if (!taskToUpdate) {
        console.error(`Task with ID ${taskId} not found.`)
        return false
      }

      // Use the PocketBase 'id' for the database service call
      const updatedTask = await databaseService.updateTask(taskToUpdate.id, taskData)

      if (updatedTask) {
        // Update local state
        const taskIndex = tasks.value.findIndex(task => task.id === taskToUpdate.id)
        if (taskIndex !== -1) {
          tasks.value[taskIndex] = { ...tasks.value[taskIndex], ...updatedTask }
        }
        return updatedTask
      }

      return false
    } catch (error_) {
      error.value = `Failed to update task: ${error_.message}`
      console.error('Error updating task:', error_)
      throw error_
    }
  }

  const addTask = async taskData => {
    try {
      // Generate a unique task ID
      const timestamp = Date.now()
      const randomSuffix = Math.random().toString(36).slice(2, 5).toUpperCase()
      const taskId = `TASK-${timestamp}-${randomSuffix}`

      // Prepare task data with defaults
      const newTaskData = {
        task_id: taskId,
        parent_id: null,
        summary: taskData.summary,
        description: taskData.description || null,
        linked_tasks: taskData.linked_tasks || [],
        epic: taskData.epic || null,
        priority: taskData.priority || 'Medium',
        estimated_effort: taskData.estimated_effort || 'Medium',
        type: taskData.type || 'Task',
        status: taskData.status || 'Backlog',
        assigned_to: taskData.assigned_to || null,
      }

      // Create task in database
      const createdTask = await databaseService.addTask(newTaskData)

      if (createdTask) {
        // Add to local state
        tasks.value.unshift(createdTask)
        return createdTask
      }

      return false
    } catch (error_) {
      error.value = `Failed to add task: ${error_.message}`
      console.error('Error adding task:', error_)
      throw error_
    }
  }

  const getTaskById = async taskId => {
    try {
      // Assuming taskId here refers to the original task_id from the JSON
      return await databaseService.getTaskByOriginalId(taskId)
    } catch (error_) {
      error.value = `Failed to fetch task: ${error_.message}`
      console.error('Error fetching task:', error_)
      throw error_
    }
  }

  const clearAllTasks = async () => {
    loading.value = true
    error.value = null

    try {
      const deletedCount = await databaseService.clearAllTasks()
      tasks.value = []
      return deletedCount
    } catch (error_) {
      error.value = `Failed to clear tasks: ${error_.message}`
      console.error('Error clearing tasks:', error_)
      throw error_
    } finally {
      loading.value = false
    }
  }

  const resetUploadState = () => {
    uploadStatus.value = 'idle'
    uploadProgress.value = 0
    lastUploadResult.value = null
    error.value = null
  }

  const searchTasks = (tasksToSearch, query) => {
    if (!query.trim()) {
      return tasksToSearch
    }

    const searchTerm = query.toLowerCase()
    return tasksToSearch.filter(task =>
      task.summary.toLowerCase().includes(searchTerm)
      || task.description?.toLowerCase().includes(searchTerm)
      || task.epic?.toLowerCase().includes(searchTerm)
      || task.task_id.toLowerCase().includes(searchTerm),
    )
  }

  const filterTasks = (tasksToFilter, filters) => {
    let filteredTasks = tasksToFilter

    if (filters.type && filters.type !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.type === filters.type)
    }

    if (filters.priority && filters.priority !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.priority === filters.priority)
    }

    if (filters.status && filters.status !== 'All') {
      // Case-insensitive comparison for status
      filteredTasks = filteredTasks.filter(task =>
        task.status && task.status.trim().toLowerCase() === filters.status.trim().toLowerCase(),
      )
    }

    if (filters.epic && filters.epic !== 'All') {
      filteredTasks = filteredTasks.filter(task => task.epic === filters.epic)
    }

    return filteredTasks
  }

  const getHierarchicalTasks = allTasks => {
    const taskMap = new Map(allTasks.map(task => [task.task_id, { ...task, children: [], incomingDependencies: [] }]))

    // Build graph: children and incoming dependencies
    for (const task of allTasks) {
      if (task.parent_id && taskMap.has(task.parent_id)) {
        taskMap.get(task.parent_id).children.push(task.task_id)
      }
      if (Array.isArray(task.linked_tasks)) {
        for (const linkedTask of task.linked_tasks) {
          if (linkedTask.linkType === 'Requires' && taskMap.has(linkedTask.task_id)) {
            taskMap.get(linkedTask.task_id).incomingDependencies.push(task.task_id)
          }
        }
      }
    }

    const orderedList = []
    const visited = new Set()
    const recursionStack = new Set() // For cycle detection

    const dfs = (taskId, level) => {
      if (recursionStack.has(taskId)) {
        console.warn(`Cycle detected in task hierarchy/dependencies involving: ${taskId}`)
        return // Break cycle
      }
      if (visited.has(taskId)) {
        return
      }

      recursionStack.add(taskId)
      visited.add(taskId)

      const task = taskMap.get(taskId)
      if (!task) {
        recursionStack.delete(taskId)
        return
      }

      // First, process tasks that this task 'Requires'
      if (Array.isArray(task.linked_tasks)) {
        for (const linkedTask of task.linked_tasks) {
          if (linkedTask.linkType === 'Requires' && taskMap.has(linkedTask.task_id)) {
            dfs(linkedTask.task_id, level) // Dependencies are at the same level or higher
          }
        }
      }

      // Add the current task to the list
      orderedList.push({ ...task, level })

      // Then, process its direct children
      task.children.sort((a, b) => a.localeCompare(b)) // Sort children for consistent order
      for (const childId of task.children) {
        dfs(childId, level + 1)
      }

      recursionStack.delete(taskId)
    }

    // Find initial roots: tasks with no parent_id AND no incoming 'Requires' dependencies
    const initialRoots = allTasks.filter(task => {
      const hasParent = task.parent_id !== null
      const hasIncomingRequires = taskMap.get(task.task_id)?.incomingDependencies.length > 0
      return !hasParent && !hasIncomingRequires
    })

    initialRoots.sort((a, b) => a.task_id.localeCompare(b.task_id))

    for (const root of initialRoots) {
      dfs(root.task_id, 0)
    }

    // Handle any remaining tasks (e.g., orphaned tasks, or tasks only part of cycles)
    for (const task of allTasks) {
      if (!visited.has(task.task_id)) {
        dfs(task.task_id, 0) // Treat as a new root if not yet visited
      }
    }

    return orderedList
  }

  return {
    // State
    tasks,
    loading,
    error,
    uploadProgress,
    uploadStatus,
    lastUploadResult,

    // Computed
    tasksByType,
    tasksByPriority,
    tasksByStatus,
    epics,
    statistics,

    // Actions
    fetchTasks,
    uploadTasksFromJson,
    updateTaskStatus,
    updateTask,
    addTask,
    getTaskById,
    clearAllTasks,
    resetUploadState,
    searchTasks,
    filterTasks,
    getHierarchicalTasks,
    logTaskStatusDistribution,
  }
})
