<template>
  <v-card elevation="2" class="mb-4">
    <v-card-title class="d-flex align-center">
      <v-icon class="me-3">mdi-filter-variant</v-icon>
      Filters & Search
      <v-spacer />
      <v-btn
        v-if="hasActiveFilters"
        variant="outlined"
        size="small"
        @click="clearAllFilters"
      >
        Clear All
      </v-btn>
    </v-card-title>
    
    <v-card-text>
      <!-- Search Bar -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-text-field
            v-model="searchQuery"
            label="Search tasks..."
            prepend-inner-icon="mdi-magnify"
            variant="outlined"
            clearable
            hide-details
            @update:model-value="updateSearch"
          >
            <template #append-inner>
              <v-badge
                v-if="searchQuery"
                :content="searchResultCount"
                color="primary"
              >
                <v-icon>mdi-text-search</v-icon>
              </v-badge>
            </template>
          </v-text-field>
        </v-col>
      </v-row>

      <!-- Filter Chips Row -->
      <v-row class="mb-4">
        <v-col cols="12">
          <div class="d-flex flex-wrap ga-2 align-center">
            <span class="text-body-2 text-medium-emphasis me-2">Quick Filters:</span>
            
            <!-- Status Quick Filters -->
            <v-chip-group
              v-model="selectedStatusFilter"
              filter
              @update:model-value="updateStatusFilter"
            >
              <v-chip
                v-for="status in quickStatusFilters"
                :key="status.value"
                :value="status.value"
                :color="status.color"
                variant="outlined"
                size="small"
              >
                <v-icon start>{{ status.icon }}</v-icon>
                {{ status.label }}
                <v-badge
                  v-if="getStatusCount(status.value) > 0"
                  :content="getStatusCount(status.value)"
                  color="white"
                  text-color="black"
                  inline
                />
              </v-chip>
            </v-chip-group>
          </div>
        </v-col>
      </v-row>

      <!-- Advanced Filters -->
      <v-expansion-panels v-model="expandedPanel" variant="accordion">
        <v-expansion-panel title="Advanced Filters">
          <v-expansion-panel-text>
            <v-row>
              <!-- Type Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.type"
                  :items="typeOptions"
                  label="Type"
                  variant="outlined"
                  hide-details
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-icon class="me-2">{{ getTypeIcon(item.value) }}</v-icon>
                      {{ item.title }}
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-icon>{{ getTypeIcon(item.value) }}</v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
              
              <!-- Priority Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.priority"
                  :items="priorityOptions"
                  label="Priority"
                  variant="outlined"
                  hide-details
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-chip size="x-small" :color="getPriorityColor(item.value)" class="me-2">
                        {{ item.title }}
                      </v-chip>
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-chip size="x-small" :color="getPriorityColor(item.value)">
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
              
              <!-- Status Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.status"
                  :items="statusOptions"
                  label="Status"
                  variant="outlined"
                  hide-details
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-chip size="x-small" :color="getStatusColor(item.value)" variant="tonal" class="me-2">
                        {{ item.title }}
                      </v-chip>
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-chip size="x-small" :color="getStatusColor(item.value)" variant="tonal">
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
              
              <!-- Epic Filter -->
              <v-col cols="12" md="3">
                <v-select
                  v-model="filters.epic"
                  :items="epicOptions"
                  label="Epic"
                  variant="outlined"
                  hide-details
                  @update:model-value="updateFilters"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center">
                      <v-icon class="me-2" color="primary">mdi-flag</v-icon>
                      {{ item.title }}
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template #prepend>
                        <v-icon color="primary">mdi-flag</v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <!-- Active Filters Display -->
      <div v-if="hasActiveFilters" class="mt-4">
        <div class="d-flex align-center flex-wrap ga-2">
          <span class="text-body-2 text-medium-emphasis">Active filters:</span>
          
          <v-chip
            v-if="filters.type !== 'All'"
            :color="getTypeColor(filters.type)"
            size="small"
            closable
            @click:close="filters.type = 'All'; updateFilters()"
          >
            <v-icon start>{{ getTypeIcon(filters.type) }}</v-icon>
            Type: {{ filters.type }}
          </v-chip>
          
          <v-chip
            v-if="filters.priority !== 'All'"
            :color="getPriorityColor(filters.priority)"
            size="small"
            closable
            @click:close="filters.priority = 'All'; updateFilters()"
          >
            Priority: {{ filters.priority }}
          </v-chip>
          
          <v-chip
            v-if="filters.status !== 'All'"
            :color="getStatusColor(filters.status)"
            size="small"
            variant="tonal"
            closable
            @click:close="filters.status = 'All'; updateFilters()"
          >
            Status: {{ filters.status }}
          </v-chip>
          
          <v-chip
            v-if="filters.epic !== 'All'"
            color="primary"
            size="small"
            variant="outlined"
            closable
            @click:close="filters.epic = 'All'; updateFilters()"
          >
            <v-icon start>mdi-flag</v-icon>
            Epic: {{ filters.epic }}
          </v-chip>
        </div>
      </div>

      <!-- Results Summary -->
      <div class="mt-4 d-flex justify-space-between align-center">
        <div class="text-body-2 text-medium-emphasis">
          Showing {{ resultCount }} of {{ totalCount }} tasks
        </div>
        
        <div class="d-flex align-center ga-2">
          <span class="text-body-2 text-medium-emphasis">View:</span>
          <v-btn-toggle v-model="viewMode" mandatory variant="outlined" density="compact">
            <v-btn value="list" icon="mdi-view-list" />
            <v-btn value="grid" icon="mdi-view-grid" />
          </v-btn-toggle>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  tasks: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: () => ({
      search: '',
      type: 'All',
      priority: 'All',
      status: 'All',
      epic: 'All'
    })
  },
  viewMode: {
    type: String,
    default: 'list'
  },
  totalTasksCount: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'update:viewMode'])

// Local state
const searchQuery = ref(props.modelValue.search || '')
const selectedStatusFilter = ref([])
const expandedPanel = ref([])
const viewMode = ref(props.viewMode)

const filters = ref({
  type: props.modelValue.type || 'All',
  priority: props.modelValue.priority || 'All',
  status: props.modelValue.status || 'All',
  epic: props.modelValue.epic || 'All'
})

// Watch for changes in modelValue.search to update searchQuery
watch(() => props.modelValue.search, (newSearch) => {
  searchQuery.value = newSearch;
});

// Computed properties
const typeOptions = computed(() => {
  const types = ['All', ...new Set(props.tasks.map(task => task.type))]
  return types.map(type => ({ title: type, value: type }))
})

const priorityOptions = computed(() => {
  const priorities = ['All', ...new Set(props.tasks.map(task => task.priority))]
  return priorities.map(priority => ({ title: priority, value: priority }))
})

const statusOptions = computed(() => {
  const statuses = ['All', ...new Set(props.tasks.map(task => task.status))]
  return statuses.map(status => ({ title: status, value: status }))
})

const epicOptions = computed(() => {
  const epics = ['All', ...new Set(props.tasks.map(task => task.epic).filter(Boolean))]
  return epics.map(epic => ({ title: epic, value: epic }))
})

const quickStatusFilters = computed(() => [
  {
    label: 'Backlog',
    value: 'Backlog',
    color: 'grey',
    icon: 'mdi-playlist-plus'
  },
  {
    label: 'In Progress',
    value: 'In Progress',
    color: 'info',
    icon: 'mdi-clock-outline'
  },
  {
    label: 'Done',
    value: 'Done',
    color: 'success',
    icon: 'mdi-check-circle'
  },
  {
    label: 'Blocked',
    value: 'Blocked',
    color: 'error',
    icon: 'mdi-block-helper'
  }
])

const hasActiveFilters = computed(() => {
  return searchQuery.value ||
         filters.value.type !== 'All' ||
         filters.value.priority !== 'All' ||
         filters.value.status !== 'All' ||
         filters.value.epic !== 'All'
})

const resultCount = computed(() => {
  let filtered = props.tasks

  if (searchQuery.value) {
    const searchTerm = searchQuery.value.toLowerCase()
    filtered = filtered.filter(task =>
      task.summary.toLowerCase().includes(searchTerm) ||
      task.description?.toLowerCase().includes(searchTerm) ||
      task.epic?.toLowerCase().includes(searchTerm) ||
      task.id.toLowerCase().includes(searchTerm)
    )
  }

  if (filters.value.type !== 'All') {
    filtered = filtered.filter(task => task.type === filters.value.type)
  }
  if (filters.value.priority !== 'All') {
    filtered = filtered.filter(task => task.priority === filters.value.priority)
  }
  if (filters.value.status !== 'All') {
    filtered = filtered.filter(task => task.status === filters.value.status)
  }
  if (filters.value.epic !== 'All') {
    filtered = filtered.filter(task => task.epic === filters.value.epic)
  }

  return filtered.length
})

const totalCount = computed(() => props.tasks.length)

const searchResultCount = computed(() => {
  if (!searchQuery.value) return 0
  const searchTerm = searchQuery.value.toLowerCase()
  return props.tasks.filter(task =>
    task.summary.toLowerCase().includes(searchTerm) ||
    task.description?.toLowerCase().includes(searchTerm) ||
    task.epic?.toLowerCase().includes(searchTerm) ||
    task.id.toLowerCase().includes(searchTerm)
  ).length
})

// Methods
const updateSearch = () => {
  emitFilters()
}

const updateFilters = () => {
  emitFilters()
}

const updateStatusFilter = (selected) => {
  if (selected.length > 0) {
    filters.value.status = selected[selected.length - 1]
  } else {
    filters.value.status = 'All'
  }
  updateFilters()
}

const emitFilters = () => {
  emit('update:modelValue', {
    search: searchQuery.value,
    type: filters.value.type,
    priority: filters.value.priority,
    status: filters.value.status,
    epic: filters.value.epic
  })
}

const clearAllFilters = () => {
  searchQuery.value = ''
  selectedStatusFilter.value = []
  filters.value = {
    type: 'All',
    priority: 'All',
    status: 'All',
    epic: 'All'
  }
  updateFilters()
}

const getStatusCount = (status) => {
  return props.tasks.filter(task => task.status === status).length
}

const getPriorityColor = (priority) => {
  switch (priority) {
    case 'High': return 'error'
    case 'Medium': return 'warning'
    case 'Low': return 'success'
    default: return 'grey'
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'Done': return 'success'
    case 'In Progress': return 'info'
    case 'Blocked': return 'error'
    case 'Backlog': return 'grey'
    default: return 'grey'
  }
}

const getTypeIcon = (type) => {
  switch (type) {
    case 'Story': return 'mdi-book-open-page-variant'
    case 'Task': return 'mdi-checkbox-marked-circle'
    case 'Epic': return 'mdi-flag'
    case 'Bug': return 'mdi-bug'
    default: return 'mdi-file-document'
  }
}

const getTypeColor = (type) => {
  switch (type) {
    case 'Story': return 'primary'
    case 'Task': return 'success'
    case 'Epic': return 'warning'
    case 'Bug': return 'error'
    default: return 'grey'
  }
}

// Watch for view mode changes
watch(() => props.viewMode, (newMode) => {
  viewMode.value = newMode
})

watch(viewMode, (newMode) => {
  emit('update:viewMode', newMode)
})
</script>

<style scoped>
.v-chip-group {
  max-width: 100%;
}
</style>
