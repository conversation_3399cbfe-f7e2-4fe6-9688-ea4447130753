{"name": "track-tasks", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "start": "node server.mjs"}, "dependencies": {"@fontsource/roboto": "5.2.5", "@mdi/font": "7.4.47", "express": "4.17.1", "markdown-it": "^14.1.0", "pocketbase": "^0.26.1", "vue": "^3.5.13", "vue3-markdown-it": "^1.0.10", "vuetify": "^3.8.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.23.0", "eslint-config-vuetify": "^4.0.0", "globals": "^16.0.0", "pinia": "^3.0.1", "sass-embedded": "^1.86.3", "unplugin-auto-import": "^19.1.1", "unplugin-fonts": "^1.3.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-router": "^0.12.0", "vite": "^6.2.2", "vite-plugin-vue-layouts-next": "^0.0.8", "vite-plugin-vuetify": "^2.1.1", "vue-router": "^4.5.0"}}